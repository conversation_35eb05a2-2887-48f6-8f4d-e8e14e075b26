<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

/**
 * Command to send shortlisting notifications to applicants
 * 
 * Usage: php spark ders:send-shortlisting-notifications [exercise_id]
 */
class SendShortlistingNotifications extends BaseCommand
{
    protected $group = 'DERS';
    protected $name = 'ders:send-shortlisting-notifications';
    protected $description = 'Send shortlisting notification emails to applicants whose applications are under shortlisting review';

    protected $usage = 'ders:send-shortlisting-notifications [exercise_id]';
    protected $arguments = [
        'exercise_id' => 'Optional. Specific exercise ID to process. If not provided, processes all exercises.'
    ];

    public function run(array $params)
    {
        CLI::write('Starting shortlisting notifications process...', 'yellow');

        $exerciseId = $params[0] ?? null;

        try {
            $applicationModel = new \App\Models\AppxApplicationDetailsModel();
            $emailService = new \App\Services\EmailService();

            // Build query for applications needing shortlisting notification
            $query = $applicationModel->select('appx_application_details.*')
                ->where('appx_application_details.rating_status', 'completed')
                ->where('(appx_application_details.shortlist_status IS NULL OR appx_application_details.shortlist_status = "")');

            // Filter by exercise if provided
            if ($exerciseId) {
                $query->where('appx_application_details.exercise_id', $exerciseId);
                CLI::write("Processing exercise ID: {$exerciseId}", 'cyan');
            } else {
                CLI::write("Processing all exercises", 'cyan');
            }

            $applications = $query->findAll();

            if (empty($applications)) {
                CLI::write('No applications found that need shortlisting notifications.', 'green');
                return;
            }

            CLI::write("Found " . count($applications) . " applications needing shortlisting notifications.", 'cyan');

            // Group applications by applicant email
            $applicationsByEmail = [];
            foreach ($applications as $application) {
                $email = $application['email_address'];
                if (!empty($email)) {
                    if (!isset($applicationsByEmail[$email])) {
                        $applicationsByEmail[$email] = [];
                    }
                    $applicationsByEmail[$email][] = $application;
                }
            }

            $successCount = 0;
            $failureCount = 0;

            // Send notification to each applicant
            foreach ($applicationsByEmail as $email => $applicantApplications) {
                try {
                    CLI::write("Sending notification to: {$email}", 'white');
                    
                    $emailResult = $emailService->sendShortlistingNotification($applicantApplications, $email);
                    
                    if ($emailResult['success']) {
                        CLI::write("✓ Notification sent successfully to: {$email}", 'green');
                        $successCount++;
                    } else {
                        CLI::write("✗ Failed to send notification to {$email}: " . $emailResult['message'], 'red');
                        $failureCount++;
                    }
                } catch (\Exception $e) {
                    CLI::write("✗ Error sending notification to {$email}: " . $e->getMessage(), 'red');
                    $failureCount++;
                }

                // Small delay to avoid overwhelming the email server
                usleep(500000); // 0.5 seconds
            }

            CLI::newLine();
            CLI::write("Shortlisting notifications process completed!", 'yellow');
            CLI::write("Successful notifications: {$successCount}", 'green');
            CLI::write("Failed notifications: {$failureCount}", 'red');
            CLI::write("Total applicants processed: " . count($applicationsByEmail), 'cyan');

        } catch (\Exception $e) {
            CLI::write("Error in shortlisting notifications process: " . $e->getMessage(), 'red');
            return EXIT_ERROR;
        }

        return EXIT_SUCCESS;
    }
}
