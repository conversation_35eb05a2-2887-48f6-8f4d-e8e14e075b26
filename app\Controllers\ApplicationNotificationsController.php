<?php

namespace App\Controllers;

use App\Models\ExerciseModel;
use App\Models\PositionsModel;
use App\Models\PositionsGroupModel;
use App\Models\AppxApplicationDetailsModel;
use App\Models\ApplicantsModel;
use App\Models\UsersModel;
use App\Services\EmailService;

class ApplicationNotificationsController extends BaseController
{
    protected $exerciseModel;
    protected $positionsModel;
    protected $positionGroupModel;
    protected $applicationModel;
    protected $applicantsModel;
    protected $usersModel;
    protected $emailService;

    public function __construct()
    {
        $this->exerciseModel = new ExerciseModel();
        $this->positionsModel = new PositionsModel();
        $this->positionGroupModel = new PositionsGroupModel();
        $this->applicationModel = new AppxApplicationDetailsModel();
        $this->applicantsModel = new ApplicantsModel();
        $this->usersModel = new UsersModel();
        $this->emailService = new EmailService();
    }

    /**
     * List exercises for notifications (Dashboard -> Exercises)
     */
    public function index()
    {
        // Get organization ID from session
        $orgId = session()->get('org_id');
        if (!$orgId) {
            session()->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Get exercises with status = 'selection' for this organization
        $exercises = $this->exerciseModel
            ->where('status', 'selection')
            ->where('org_id', $orgId)
            ->orderBy('created_at', 'DESC')
            ->findAll();

        // Add notification counts for each exercise using model methods
        foreach ($exercises as &$exercise) {
            $exercise['total_applications'] = $this->getTotalApplicationsForExercise($exercise['id']);
            $exercise['notifications_not_sent'] = $this->getNotificationsNotSentForExercise($exercise['id']);
            $exercise['notifications_sent'] = $this->getNotificationsSentForExercise($exercise['id']);
        }

        $data = [
            'title' => 'Applications Notifications - Select Exercise',
            'menu' => 'notifications',
            'exercises' => $exercises
        ];

        return view('application_notifications/application_notifications_exercises', $data);
    }

    /**
     * List position groups for an exercise (Exercises -> Position Groups)
     */
    public function positionGroups($exerciseId)
    {
        // Get exercise details
        $exercise = $this->exerciseModel->select('
                exercises.id,
                exercises.exercise_name,
                exercises.advertisement_no,
                dakoii_org.org_name
            ')
            ->join('dakoii_org', 'exercises.org_id = dakoii_org.id', 'left')
            ->where('exercises.id', $exerciseId)
            ->first();

        if (!$exercise) {
            return redirect()->to('/applications/notifications')->with('error', 'Exercise not found');
        }

        // Get position groups for this exercise
        $positionGroups = $this->positionGroupModel
            ->where('exercise_id', $exerciseId)
            ->orderBy('group_name', 'ASC')
            ->findAll();

        // Add notification counts for each position group using model methods
        foreach ($positionGroups as &$positionGroup) {
            $positionGroup['position_count'] = $this->getPositionCountForGroup($positionGroup['id']);
            $positionGroup['total_applications'] = $this->getTotalApplicationsForPositionGroup($positionGroup['id']);
            $positionGroup['notifications_not_sent'] = $this->getNotificationsNotSentForPositionGroup($positionGroup['id']);
            $positionGroup['notifications_sent'] = $this->getNotificationsSentForPositionGroup($positionGroup['id']);
        }

        $data = [
            'title' => 'Applications Notifications - Select Position Group',
            'menu' => 'notifications',
            'exercise' => $exercise,
            'positionGroups' => $positionGroups
        ];

        return view('application_notifications/application_notifications_position_groups', $data);
    }

    /**
     * List positions for a position group (Position Groups -> Positions)
     */
    public function positions($positionGroupId)
    {
        // Get position group details
        $positionGroup = $this->positionGroupModel->select('
                positions_groups.id,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                dakoii_org.org_name
            ')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions_groups.org_id = dakoii_org.id', 'left')
            ->where('positions_groups.id', $positionGroupId)
            ->first();

        if (!$positionGroup) {
            return redirect()->to('/applications/notifications')->with('error', 'Position group not found');
        }

        // Get positions in this group
        $positions = $this->positionsModel
            ->where('position_group_id', $positionGroupId)
            ->orderBy('designation', 'ASC')
            ->findAll();

        // Add notification counts for each position using model methods
        foreach ($positions as &$position) {
            $position['total_applications'] = $this->getTotalApplicationsForPosition($position['id']);
            $position['shortlisted_count'] = $this->getShortlistedCountForPosition($position['id']);
            $position['eliminated_count'] = $this->getEliminatedCountForPosition($position['id']);
            $position['notifications_not_sent'] = $this->getNotificationsNotSentForPosition($position['id']);
            $position['notifications_sent'] = $this->getNotificationsSentForPosition($position['id']);
        }

        $data = [
            'title' => 'Applications Notifications - Select Position',
            'menu' => 'notifications',
            'positionGroup' => $positionGroup,
            'positions' => $positions
        ];

        return view('application_notifications/application_notifications_positions', $data);
    }

    /**
     * List applications for a position (Positions -> Applications) - Show shortlisted/eliminated applications
     */
    public function applications($positionId)
    {
        // Get position details with related information
        $position = $this->positionsModel->select('
                positions.*,
                positions_groups.id as position_group_id,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                exercises.gazzetted_no,
                exercises.gazzetted_date,
                dakoii_org.org_name
            ')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->where('positions.id', $positionId)
            ->first();

        if (!$position) {
            return redirect()->to('/applications/notifications')->with('error', 'Position not found');
        }

        // Get applications for this position that have been shortlisted or eliminated
        $applications = $this->applicationModel->select('
                appx_application_details.*,
                positions.designation as position_title,
                users.name as notified_by_name
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->join('users', 'appx_application_details.shortlisting_notified_by = users.id', 'left')
            ->where('appx_application_details.position_id', $positionId)
            ->whereIn('appx_application_details.shortlist_status', ['shortlisted', 'eliminated'])
            ->orderBy('appx_application_details.shortlist_status', 'DESC') // shortlisted first
            ->orderBy('appx_application_details.rating_capability_max', 'DESC')
            ->findAll();

        $data = [
            'title' => 'Applications Notifications - Send Notifications',
            'menu' => 'notifications',
            'position' => $position,
            'applications' => $applications
        ];

        return view('application_notifications/application_notifications_applications', $data);
    }

    /**
     * Send successful shortlisting notification (POST method)
     */
    public function sendSuccessfulNotification($applicationId)
    {
        // Get application details with related data
        $application = $this->applicationModel->getApplicationWithDetails($applicationId);
        
        if (!$application) {
            return redirect()->back()->with('error', 'Application not found');
        }

        // Check if application is shortlisted
        if ($application['shortlist_status'] !== 'shortlisted') {
            return redirect()->back()->with('error', 'Application is not shortlisted');
        }

        // Check if notification already sent (all three fields must be filled)
        if (!empty($application['shortlisting_notified_at']) &&
            !empty($application['shortlisting_notified_status']) &&
            !empty($application['shortlisting_notified_by'])) {
            return redirect()->back()->with('warning', 'Notification already sent for this application');
        }

        // Get applicant email from applicants table
        $applicant = $this->applicantsModel->find($application['applicant_id']);
        if (!$applicant || empty($applicant['email'])) {
            return redirect()->back()->with('error', 'Applicant email not found');
        }

        // Send successful notification email
        $emailResult = $this->emailService->sendSuccessfulShortlistingNotification($application, $applicant['email']);

        if ($emailResult['success']) {
            // Update notification fields
            $this->applicationModel->update($applicationId, [
                'shortlisting_notified_at' => date('Y-m-d H:i:s'),
                'shortlisting_notified_status' => 'successful',
                'shortlisting_notified_by' => session()->get('user_id')
            ]);

            return redirect()->back()->with('success', 'Successful shortlisting notification sent to ' . $applicant['email']);
        } else {
            return redirect()->back()->with('error', 'Failed to send notification: ' . $emailResult['message']);
        }
    }

    /**
     * Send unsuccessful shortlisting notification (POST method)
     */
    public function sendUnsuccessfulNotification($applicationId)
    {
        // Get application details with related data
        $application = $this->applicationModel->getApplicationWithDetails($applicationId);
        
        if (!$application) {
            return redirect()->back()->with('error', 'Application not found');
        }

        // Check if application is eliminated
        if ($application['shortlist_status'] !== 'eliminated') {
            return redirect()->back()->with('error', 'Application is not eliminated');
        }

        // Check if notification already sent (all three fields must be filled)
        if (!empty($application['shortlisting_notified_at']) &&
            !empty($application['shortlisting_notified_status']) &&
            !empty($application['shortlisting_notified_by'])) {
            return redirect()->back()->with('warning', 'Notification already sent for this application');
        }

        // Get applicant email from applicants table
        $applicant = $this->applicantsModel->find($application['applicant_id']);
        if (!$applicant || empty($applicant['email'])) {
            return redirect()->back()->with('error', 'Applicant email not found');
        }

        // Send unsuccessful notification email
        $emailResult = $this->emailService->sendUnsuccessfulShortlistingNotification($application, $applicant['email']);

        if ($emailResult['success']) {
            // Update notification fields
            $this->applicationModel->update($applicationId, [
                'shortlisting_notified_at' => date('Y-m-d H:i:s'),
                'shortlisting_notified_status' => 'unsuccessful',
                'shortlisting_notified_by' => session()->get('user_id')
            ]);

            return redirect()->back()->with('success', 'Unsuccessful shortlisting notification sent to ' . $applicant['email']);
        } else {
            return redirect()->back()->with('error', 'Failed to send notification: ' . $emailResult['message']);
        }
    }

    /**
     * Get total applications count for an exercise
     */
    private function getTotalApplicationsForExercise($exerciseId)
    {
        return $this->applicationModel
            ->join('positions', 'appx_application_details.position_id = positions.id')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id')
            ->where('positions_groups.exercise_id', $exerciseId)
            ->where('appx_application_details.deleted_at IS NULL')
            ->countAllResults();
    }

    /**
     * Get notifications not sent count for an exercise using the comprehensive logic
     */
    private function getNotificationsNotSentForExercise($exerciseId)
    {
        return $this->applicationModel
            ->join('positions', 'appx_application_details.position_id = positions.id')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id')
            ->where('positions_groups.exercise_id', $exerciseId)
            ->whereIn('appx_application_details.shortlist_status', ['shortlisted', 'eliminated'])
            ->groupStart()
                ->where('appx_application_details.shortlisting_notified_at IS NULL')
                ->orWhere('appx_application_details.shortlisting_notified_at', '')
                ->orWhere('appx_application_details.shortlisting_notified_status IS NULL')
                ->orWhere('appx_application_details.shortlisting_notified_status', '')
                ->orWhere('appx_application_details.shortlisting_notified_by IS NULL')
                ->orWhere('appx_application_details.shortlisting_notified_by', '')
            ->groupEnd()
            ->where('appx_application_details.deleted_at IS NULL')
            ->countAllResults();
    }

    /**
     * Get notifications sent count for an exercise using the comprehensive logic
     */
    private function getNotificationsSentForExercise($exerciseId)
    {
        return $this->applicationModel
            ->join('positions', 'appx_application_details.position_id = positions.id')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id')
            ->where('positions_groups.exercise_id', $exerciseId)
            ->whereIn('appx_application_details.shortlist_status', ['shortlisted', 'eliminated'])
            ->where('appx_application_details.shortlisting_notified_at IS NOT NULL')
            ->where('appx_application_details.shortlisting_notified_at !=', '')
            ->where('appx_application_details.shortlisting_notified_status IS NOT NULL')
            ->where('appx_application_details.shortlisting_notified_status !=', '')
            ->where('appx_application_details.shortlisting_notified_by IS NOT NULL')
            ->where('appx_application_details.shortlisting_notified_by !=', '')
            ->where('appx_application_details.deleted_at IS NULL')
            ->countAllResults();
    }

    /**
     * Get position count for a position group
     */
    private function getPositionCountForGroup($positionGroupId)
    {
        return $this->positionsModel
            ->where('position_group_id', $positionGroupId)
            ->where('deleted_at IS NULL')
            ->countAllResults();
    }

    /**
     * Get total applications count for a position group
     */
    private function getTotalApplicationsForPositionGroup($positionGroupId)
    {
        return $this->applicationModel
            ->join('positions', 'appx_application_details.position_id = positions.id')
            ->where('positions.position_group_id', $positionGroupId)
            ->where('appx_application_details.deleted_at IS NULL')
            ->countAllResults();
    }

    /**
     * Get notifications not sent count for a position group
     */
    private function getNotificationsNotSentForPositionGroup($positionGroupId)
    {
        return $this->applicationModel
            ->join('positions', 'appx_application_details.position_id = positions.id')
            ->where('positions.position_group_id', $positionGroupId)
            ->whereIn('appx_application_details.shortlist_status', ['shortlisted', 'eliminated'])
            ->groupStart()
                ->where('appx_application_details.shortlisting_notified_at IS NULL')
                ->orWhere('appx_application_details.shortlisting_notified_at', '')
                ->orWhere('appx_application_details.shortlisting_notified_status IS NULL')
                ->orWhere('appx_application_details.shortlisting_notified_status', '')
                ->orWhere('appx_application_details.shortlisting_notified_by IS NULL')
                ->orWhere('appx_application_details.shortlisting_notified_by', '')
            ->groupEnd()
            ->where('appx_application_details.deleted_at IS NULL')
            ->countAllResults();
    }

    /**
     * Get notifications sent count for a position group
     */
    private function getNotificationsSentForPositionGroup($positionGroupId)
    {
        return $this->applicationModel
            ->join('positions', 'appx_application_details.position_id = positions.id')
            ->where('positions.position_group_id', $positionGroupId)
            ->whereIn('appx_application_details.shortlist_status', ['shortlisted', 'eliminated'])
            ->where('appx_application_details.shortlisting_notified_at IS NOT NULL')
            ->where('appx_application_details.shortlisting_notified_at !=', '')
            ->where('appx_application_details.shortlisting_notified_status IS NOT NULL')
            ->where('appx_application_details.shortlisting_notified_status !=', '')
            ->where('appx_application_details.shortlisting_notified_by IS NOT NULL')
            ->where('appx_application_details.shortlisting_notified_by !=', '')
            ->where('appx_application_details.deleted_at IS NULL')
            ->countAllResults();
    }

    /**
     * Get total applications count for a position
     */
    private function getTotalApplicationsForPosition($positionId)
    {
        return $this->applicationModel
            ->where('position_id', $positionId)
            ->where('deleted_at IS NULL')
            ->countAllResults();
    }

    /**
     * Get shortlisted applications count for a position
     */
    private function getShortlistedCountForPosition($positionId)
    {
        return $this->applicationModel
            ->where('position_id', $positionId)
            ->where('shortlist_status', 'shortlisted')
            ->where('deleted_at IS NULL')
            ->countAllResults();
    }

    /**
     * Get eliminated applications count for a position
     */
    private function getEliminatedCountForPosition($positionId)
    {
        return $this->applicationModel
            ->where('position_id', $positionId)
            ->where('shortlist_status', 'eliminated')
            ->where('deleted_at IS NULL')
            ->countAllResults();
    }

    /**
     * Get notifications not sent count for a position
     */
    private function getNotificationsNotSentForPosition($positionId)
    {
        return $this->applicationModel
            ->where('position_id', $positionId)
            ->whereIn('shortlist_status', ['shortlisted', 'eliminated'])
            ->groupStart()
                ->where('shortlisting_notified_at IS NULL')
                ->orWhere('shortlisting_notified_at', '')
                ->orWhere('shortlisting_notified_status IS NULL')
                ->orWhere('shortlisting_notified_status', '')
                ->orWhere('shortlisting_notified_by IS NULL')
                ->orWhere('shortlisting_notified_by', '')
            ->groupEnd()
            ->where('deleted_at IS NULL')
            ->countAllResults();
    }

    /**
     * Get notifications sent count for a position
     */
    private function getNotificationsSentForPosition($positionId)
    {
        return $this->applicationModel
            ->where('position_id', $positionId)
            ->whereIn('shortlist_status', ['shortlisted', 'eliminated'])
            ->where('shortlisting_notified_at IS NOT NULL')
            ->where('shortlisting_notified_at !=', '')
            ->where('shortlisting_notified_status IS NOT NULL')
            ->where('shortlisting_notified_status !=', '')
            ->where('shortlisting_notified_by IS NOT NULL')
            ->where('shortlisting_notified_by !=', '')
            ->where('deleted_at IS NULL')
            ->countAllResults();
    }
}
