<?php
/**
 * View file for listing exercises available for application notifications
 *
 * @var array $exercises List of exercises in 'selection' status
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-6">
            <h2><i class="fas fa-bell me-2"></i>Applications Notifications</h2>
            <p class="text-muted">Selection exercises available for sending application notifications</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?= base_url('dashboard') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-body">
            <?php if (empty($exercises)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Selection Exercises Available</h5>
                    <p class="text-muted">There are no exercises in selection status available for application notifications.</p>
                    <a href="<?= base_url('exercises') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Manage Exercises
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Exercise Details</th>
                                <th>Advertisement No.</th>
                                <th>Published From</th>
                                <th>Published To</th>
                                <th class="text-center">Notifications Not Sent</th>
                                <th class="text-center">Notifications Sent</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($exercises as $exercise): ?>
                                <tr>
                                    <td>
                                        <div class="fw-bold"><?= esc($exercise['exercise_name']) ?></div>
                                        <small class="text-muted">Gazzetted: <?= esc($exercise['gazzetted_no']) ?></small>
                                    </td>
                                    <td><?= esc($exercise['advertisement_no']) ?></td>
                                    <td><?= date('M d, Y', strtotime($exercise['publish_date_from'])) ?></td>
                                    <td><?= date('M d, Y', strtotime($exercise['publish_date_to'])) ?></td>
                                    <td class="text-center">
                                        <?php if ($exercise['notifications_not_sent'] > 0): ?>
                                            <span class="badge bg-warning"><?= number_format($exercise['notifications_not_sent']) ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">0</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-success"><?= number_format($exercise['notifications_sent']) ?></span>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('applications/notifications/position-groups/' . $exercise['id']) ?>"
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-bell me-1"></i> Send Notifications
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
