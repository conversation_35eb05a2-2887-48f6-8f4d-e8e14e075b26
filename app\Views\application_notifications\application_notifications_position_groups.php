<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="card mb-4 border-start border-primary border-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h4 fw-bold text-gray-800 mb-1">Applications Notifications - Select Position Group</h2>
                    <p class="text-muted mb-0">Select a position group to view positions for sending notifications</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('applications/notifications') ?>" class="text-decoration-none">Exercises</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Position Groups</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Exercise Information -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h5 class="card-title mb-3">
                        <i class="fas fa-clipboard-list me-2 text-primary"></i>
                        <?= esc($exercise['exercise_name']) ?>
                    </h5>
                    <div class="row">
                        <div class="col-sm-6">
                            <p class="mb-1"><strong>Organization:</strong> <?= esc($exercise['org_name']) ?></p>
                            <p class="mb-1"><strong>Advertisement No:</strong> <?= esc($exercise['advertisement_no']) ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <a href="<?= base_url('applications/notifications') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Exercises
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Position Groups List -->
    <div class="card">
        <div class="card-body">
            <?php if (empty($positionGroups)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Position Groups Available</h5>
                    <p class="text-muted">There are no position groups available for this exercise.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Position Group</th>
                                <th class="text-center">Total Positions</th>
                                <th class="text-center">Notifications Not Sent</th>
                                <th class="text-center">Notifications Sent</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($positionGroups as $group): ?>
                                <tr>
                                    <td>
                                        <div class="fw-bold"><?= esc($group['group_name']) ?></div>
                                        <small class="text-muted">Group ID: <?= esc($group['id']) ?></small>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-info"><?= number_format($group['position_count']) ?></span>
                                    </td>
                                    <td class="text-center">
                                        <?php if ($group['notifications_not_sent'] > 0): ?>
                                            <span class="badge bg-warning"><?= number_format($group['notifications_not_sent']) ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">0</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-success"><?= number_format($group['notifications_sent']) ?></span>
                                    </td>
                                    <td class="text-center">
                                        <a href="<?= base_url('applications/notifications/positions/' . $group['id']) ?>"
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-eye me-1"></i> View Positions
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
