<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="card mb-4 border-start border-primary border-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h4 fw-bold text-gray-800 mb-1">Applications Notifications - Select Position</h2>
                    <p class="text-muted mb-0">Select a position to view applications for sending notifications</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('applications/notifications') ?>" class="text-decoration-none">Exercises</a></li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('applications/notifications/position-groups/' . $positionGroup['exercise_id']) ?>" class="text-decoration-none">
                                Position Groups
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Positions</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Position Group Information -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h5 class="card-title mb-3">
                        <i class="fas fa-layer-group me-2 text-primary"></i>
                        <?= esc($positionGroup['group_name']) ?>
                    </h5>
                    <div class="row">
                        <div class="col-sm-6">
                            <p class="mb-1"><strong>Exercise:</strong> <?= esc($positionGroup['exercise_name']) ?></p>
                            <p class="mb-1"><strong>Organization:</strong> <?= esc($positionGroup['org_name']) ?></p>
                        </div>
                        <div class="col-sm-6">
                            <p class="mb-1"><strong>Advertisement No:</strong> <?= esc($positionGroup['advertisement_no']) ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <a href="<?= base_url('applications/notifications/position-groups/' . $positionGroup['exercise_id']) ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Position Groups
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Positions List -->
    <div class="card">
        <div class="card-body">
            <?php if (empty($positions)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Positions with Shortlisted/Eliminated Applications</h5>
                    <p class="text-muted">There are no positions with applications that have been shortlisted or eliminated.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Position Details</th>
                                <th>Classification</th>
                                <th>Location</th>
                                <th class="text-center">Total Applications</th>
                                <th class="text-center">Shortlisted</th>
                                <th class="text-center">Eliminated</th>
                                <th class="text-center">Notifications Not Sent</th>
                                <th class="text-center">Notifications Sent</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($positions as $position): ?>
                                <tr>
                                    <td>
                                        <div class="fw-bold"><?= esc($position['designation']) ?></div>
                                        <small class="text-muted">ID: <?= esc($position['id']) ?></small>
                                    </td>
                                    <td><?= esc($position['classification']) ?></td>
                                    <td><?= esc($position['location']) ?></td>
                                    <td class="text-center">
                                        <span class="badge bg-info"><?= number_format($position['total_applications']) ?></span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-success"><?= number_format($position['shortlisted_count']) ?></span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-danger"><?= number_format($position['eliminated_count']) ?></span>
                                    </td>
                                    <td class="text-center">
                                        <?php if ($position['notifications_not_sent'] > 0): ?>
                                            <span class="badge bg-warning"><?= number_format($position['notifications_not_sent']) ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">0</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-success"><?= number_format($position['notifications_sent']) ?></span>
                                    </td>
                                    <td class="text-center">
                                        <?php if ($position['total_applications'] > 0): ?>
                                            <a href="<?= base_url('applications/notifications/applications/' . $position['id']) ?>"
                                               class="btn btn-primary btn-sm">
                                                <i class="fas fa-bell me-1"></i> Send Notifications
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">No applications</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
