<?php
/**
 * View file for Application Register Report
 *
 * @var array $position Position details
 * @var array $applications List of applications
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Application Register - <?= esc($exercise['exercise_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Application Register Report</h2>
                    <p class="text-muted mb-0"><?= esc($exercise['exercise_name']) ?></p>
                </div>
                <div>
                    <button type="button" class="btn btn-success me-2" onclick="exportReport()">
                        <i class="fas fa-file-pdf me-1"></i>
                        Export PDF
                    </button>
                    <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Application Summary Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="border rounded p-3 h-100">
                                <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                                <h4 class="text-primary mb-1"><?= $statistics['total_applications'] ?></h4>
                                <p class="mb-0 text-muted">Total Applications</p>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="border rounded p-3 h-100">
                                <i class="fas fa-users fa-2x text-success mb-2"></i>
                                <h4 class="text-success mb-1"><?= $statistics['total_applicants'] ?></h4>
                                <p class="mb-0 text-muted">Total Applicants</p>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?= base_url('reports/positions-with-applications/' . $exercise['id']) ?>"
                               class="text-decoration-none">
                                <div class="border rounded p-3 h-100 position-card-hover">
                                    <i class="fas fa-briefcase fa-2x text-info mb-2"></i>
                                    <h4 class="text-info mb-1"><?= $statistics['total_positions_with_applications'] ?></h4>
                                    <p class="mb-0 text-muted">Positions with Applications</p>
                                    <small class="text-info">
                                        <i class="fas fa-eye me-1"></i>Click to view details
                                    </small>
                                </div>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?= base_url('reports/positions-without-applications/' . $exercise['id']) ?>"
                               class="text-decoration-none">
                                <div class="border rounded p-3 h-100 position-card-hover">
                                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                                    <h4 class="text-warning mb-1"><?= $statistics['total_positions_without_applications'] ?></h4>
                                    <p class="mb-0 text-muted">Positions without Applications</p>
                                    <small class="text-warning">
                                        <i class="fas fa-eye me-1"></i>Click to view details
                                    </small>
                                </div>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?= base_url('reports/registration-by-positions/' . $exercise['id']) ?>"
                               class="text-decoration-none">
                                <div class="border rounded p-3 h-100 position-card-hover">
                                    <i class="fas fa-layer-group fa-2x text-secondary mb-2"></i>
                                    <h4 class="text-secondary mb-1"><?= $statistics['total_positions'] ?></h4>
                                    <p class="mb-0 text-muted">Registration by Positions</p>
                                    <small class="text-secondary">
                                        <i class="fas fa-eye me-1"></i>Click to view details
                                    </small>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications Register -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        Application Register (<?= count($applications) ?> Applications)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($applications)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No applications found</h5>
                            <p class="text-muted">No applications have been received for this position.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="applicationsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Application No.</th>
                                        <th>Full Name</th>
                                        <th>Gender</th>
                                        <th>Email</th>
                                        <th>Position Ref</th>
                                        <th>Position Title</th>
                                        <th>Received Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($applications as $index => $application): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td><strong><?= esc($application['application_number']) ?></strong></td>
                                            <td><strong><?= esc($application['full_name']) ?></strong></td>
                                            <td><?= esc($application['gender']) ?></td>
                                            <td><?= esc($application['email_address']) ?></td>
                                            <td><?= esc($application['position_reference'] ?? 'N/A') ?></td>
                                            <td><?= esc($application['position_title'] ?? 'N/A') ?></td>
                                            <td><?= date('M d, Y', strtotime($application['created_at'])) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>


</div>

<!-- DataTables Buttons extension for export functionality -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>

<script>
function exportReport() {
    const button = event.target.closest('button');
    const originalText = button.innerHTML;

    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Exporting...';
    button.disabled = true;

    // Get exercise ID from the current page
    const exerciseId = <?= $exercise['id'] ?>;

    // Get CSRF token from meta tag
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Create a temporary form for PDF download
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '<?= base_url('reports/application-register/export') ?>';
    form.target = '_blank'; // Open in new tab
    form.style.display = 'none';

    // Add exercise ID
    const exerciseInput = document.createElement('input');
    exerciseInput.type = 'hidden';
    exerciseInput.name = 'exercise_id';
    exerciseInput.value = exerciseId;
    form.appendChild(exerciseInput);

    // Add CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '<?= csrf_token() ?>';
    csrfInput.value = csrfToken;
    form.appendChild(csrfInput);

    // Add form to document and submit
    document.body.appendChild(form);
    form.submit();

    // Clean up
    setTimeout(() => {
        document.body.removeChild(form);
        button.innerHTML = originalText;
        button.disabled = false;
    }, 1000);
}

// Initialize DataTable if available
document.addEventListener('DOMContentLoaded', function() {
    if (typeof $ !== 'undefined' && $.fn.DataTable) {
        $('#applicationsTable').DataTable({
            responsive: true,
            paging: false, // Disable pagination
            order: [[7, 'desc']], // Sort by received date (newest first)
            columnDefs: [
                { orderable: false, targets: [0] } // Disable sorting for # column
            ],
            language: {
                info: "Showing _TOTAL_ applications",
                infoEmpty: "No applications available",
                infoFiltered: "(filtered from _MAX_ total applications)"
            },
            dom: 'Bfrtip',
            buttons: [
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel me-1"></i> Export to Excel',
                    className: 'btn btn-success',
                    filename: 'Application_Register_<?= str_replace(' ', '_', $exercise['exercise_name']) ?>_<?= date('Y-m-d') ?>',
                    title: 'Application Register - <?= esc($exercise['exercise_name']) ?>',
                    exportOptions: {
                        columns: ':visible'
                    }
                }
            ]
        });
    }
});
</script>
<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.position-card-hover {
    transition: all 0.3s ease;
    cursor: pointer;
}

.position-card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-color: #007bff !important;
}
</style>
<?= $this->endSection() ?>
