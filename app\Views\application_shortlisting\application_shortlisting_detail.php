<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="card mb-4 border-start border-primary border-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h4 fw-bold text-gray-800 mb-1">Shortlist Application</h2>
                    <p class="text-muted mb-0">Review and update shortlisting status</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('shortlisting') ?>" class="text-decoration-none">Exercises</a></li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('shortlisting/position-groups/' . $position['exercise_id']) ?>" class="text-decoration-none">
                                Position Groups
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('shortlisting/positions/' . $position['position_group_id']) ?>" class="text-decoration-none">
                                Positions
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('shortlisting/applications/' . $position['id']) ?>" class="text-decoration-none">
                                Applications
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Shortlist</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Application Information -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>
                        Application Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Personal Information</h6>
                            <p><strong>Name:</strong> <?= esc($application['first_name']) ?> <?= esc($application['last_name']) ?></p>
                            <p><strong>Application No:</strong> <?= esc($application['application_number']) ?></p>
                            <p><strong>Gender:</strong> <?= esc($application['gender']) ?></p>
                            <p><strong>Date of Birth:</strong> <?= date('M d, Y', strtotime($application['date_of_birth'])) ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Position Information</h6>
                            <p><strong>Position:</strong> <?= esc($position['designation']) ?></p>
                            <p><strong>Classification:</strong> <?= esc($position['classification']) ?></p>
                            <p><strong>Location:</strong> <?= esc($position['location']) ?></p>
                            <p><strong>Exercise:</strong> <?= esc($position['exercise_name']) ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pre-Screening Information -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter me-2"></i>
                        Pre-Screening Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Pre-Screening Status:</strong>
                                <?php
                                $preScreenStatus = $application['pre_screened_status'] ?? 'Not Screened';
                                $badgeClass = match($preScreenStatus) {
                                    'passed' => 'bg-success',
                                    'failed' => 'bg-danger',
                                    default => 'bg-secondary'
                                };
                                ?>
                                <span class="badge <?= $badgeClass ?>"><?= ucfirst($preScreenStatus) ?></span>
                            </p>
                            <?php if (!empty($application['pre_screened_at'])): ?>
                                <p><strong>Screened On:</strong> <?= date('M d, Y H:i', strtotime($application['pre_screened_at'])) ?></p>
                            <?php endif; ?>
                            <?php if (!empty($application['pre_screened_remarks'])): ?>
                                <p><strong>Pre-Screening Remarks:</strong></p>
                                <p class="text-muted small"><?= esc($application['pre_screened_remarks']) ?></p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <?php if (!empty($application['pre_screened_criteria_results'])): ?>
                                <p><strong>Pre-Screening Criteria Results:</strong></p>
                                <div class="border rounded p-3 bg-light">
                                    <?php
                                    $criteriaResults = json_decode($application['pre_screened_criteria_results'], true);
                                    if ($criteriaResults && is_array($criteriaResults)):
                                    ?>
                                        <div class="small">
                                            <?php foreach ($criteriaResults as $criterion => $result): ?>
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span><?= esc($criterion) ?>:</span>
                                                    <span class="badge <?= $result ? 'bg-success' : 'bg-danger' ?>">
                                                        <?= $result ? 'Met' : 'Not Met' ?>
                                                    </span>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-muted small">
                                            <?= esc($application['pre_screened_criteria_results']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <p class="text-muted">No pre-screening criteria results available.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rating Information -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-star me-2"></i>
                        Rating Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="text-center">
                                <h3 class="text-primary"><?= $application['rating_capability_max'] ?? 0 ?></h3>
                                <p class="text-muted">Total Score</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Rating Status:</strong>
                                <span class="badge bg-success"><?= ucfirst($application['rating_status']) ?></span>
                            </p>
                            <?php if (!empty($application['rating_remarks'])): ?>
                                <p><strong>Rating Remarks:</strong></p>

                                <!-- Markdown Viewer Toggle -->
                                <div class="mb-2">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <input type="radio" class="btn-check" name="remarkViewMode" id="remarkMarkdownView" checked>
                                        <label class="btn btn-outline-primary" for="remarkMarkdownView">
                                            <i class="fas fa-eye me-1"></i>Formatted
                                        </label>

                                        <input type="radio" class="btn-check" name="remarkViewMode" id="remarkRawView">
                                        <label class="btn btn-outline-secondary" for="remarkRawView">
                                            <i class="fas fa-code me-1"></i>Raw
                                        </label>
                                    </div>
                                </div>

                                <!-- Markdown Content -->
                                <div class="border rounded p-3 bg-light">
                                    <div id="rating_remarks_formatted" style="display: block;">
                                        <!-- Formatted markdown content will be inserted here -->
                                    </div>
                                    <pre id="rating_remarks_raw" class="mb-0 small" style="display: none; white-space: pre-wrap;"><?= esc($application['rating_remarks']) ?></pre>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shortlisting Form -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clipboard-check me-2"></i>
                        Shortlisting Decision
                    </h5>
                </div>
                <div class="card-body">
                    <?= form_open('shortlisting/update/' . $application['id']) ?>
                        <div class="mb-3">
                            <label for="shortlist_status" class="form-label">Shortlist Status <span class="text-danger">*</span></label>
                            <select class="form-select" id="shortlist_status" name="shortlist_status" required>
                                <option value="">Select Status</option>
                                <option value="shortlisted" <?= (old('shortlist_status') == 'shortlisted' || $application['shortlist_status'] == 'shortlisted') ? 'selected' : '' ?>>
                                    Shortlisted
                                </option>
                                <option value="eliminated" <?= (old('shortlist_status') == 'eliminated' || $application['shortlist_status'] == 'eliminated') ? 'selected' : '' ?>>
                                    Eliminated
                                </option>
                                <option value="withdrawn" <?= (old('shortlist_status') == 'withdrawn' || $application['shortlist_status'] == 'withdrawn') ? 'selected' : '' ?>>
                                    Withdrawn
                                </option>
                            </select>
                            <?php if (isset($validation) && $validation->hasError('shortlist_status')): ?>
                                <div class="text-danger small"><?= $validation->getError('shortlist_status') ?></div>
                            <?php endif; ?>
                        </div>

                        <div class="mb-3">
                            <label for="shortlist_remarks" class="form-label">Remarks</label>
                            <textarea class="form-control" id="shortlist_remarks" name="shortlist_remarks" rows="4" 
                                      placeholder="Enter remarks or justification for the shortlisting decision..."><?= old('shortlist_remarks', $application['shortlist_remarks'] ?? '') ?></textarea>
                            <?php if (isset($validation) && $validation->hasError('shortlist_remarks')): ?>
                                <div class="text-danger small"><?= $validation->getError('shortlist_remarks') ?></div>
                            <?php endif; ?>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Shortlist Status
                            </button>
                            <a href="<?= base_url('shortlisting/applications/' . $position['id']) ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Applications
                            </a>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>

            <!-- Current Status -->
            <?php if (!empty($application['shortlist_status'])): ?>
                <div class="card mt-3">
                    <div class="card-header bg-info text-white">
                        <h6 class="card-title mb-0">Current Status</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>Status:</strong> 
                            <?php
                            $status = $application['shortlist_status'];
                            $badgeClass = match($status) {
                                'shortlisted' => 'bg-success',
                                'eliminated' => 'bg-danger',
                                'withdrawn' => 'bg-warning',
                                default => 'bg-secondary'
                            };
                            ?>
                            <span class="badge <?= $badgeClass ?>"><?= ucfirst($status) ?></span>
                        </p>
                        <?php if (!empty($application['shortlisted_at'])): ?>
                            <p><strong>Updated:</strong> <?= date('M d, Y H:i', strtotime($application['shortlisted_at'])) ?></p>
                        <?php endif; ?>
                        <?php if (!empty($application['shortlist_remarks'])): ?>
                            <p><strong>Remarks:</strong></p>
                            <p class="text-muted small"><?= esc($application['shortlist_remarks']) ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Marked.js for Markdown Rendering -->
<script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>

<script>
$(document).ready(function() {
    // Configure marked.js for security and styling
    marked.setOptions({
        breaks: true,
        gfm: true,
        sanitize: false, // We'll handle sanitization manually
        smartLists: true,
        smartypants: true
    });

    // Custom renderer for Bootstrap styling
    const renderer = new marked.Renderer();

    // Style headers with Bootstrap classes
    renderer.heading = function(text, level) {
        const classes = {
            1: 'h4 mt-3 mb-2 text-primary',
            2: 'h5 mt-3 mb-2 text-primary',
            3: 'h6 mt-3 mb-2 text-primary',
            4: 'h6 mt-2 mb-2 text-secondary',
            5: 'small mt-2 mb-1 text-secondary fw-bold',
            6: 'small mt-2 mb-1 text-muted fw-bold'
        };
        const className = classes[level] || 'h6 mt-2 mb-2';
        return `<h${level} class="${className}">${text}</h${level}>`;
    };

    // Style lists with Bootstrap classes
    renderer.list = function(body, ordered) {
        const type = ordered ? 'ol' : 'ul';
        return `<${type} class="mb-2">${body}</${type}>`;
    };

    // Style code with Bootstrap classes
    renderer.code = function(code, language) {
        return `<pre class="bg-light border rounded p-2 small"><code>${code}</code></pre>`;
    };

    renderer.codespan = function(code) {
        return `<code class="bg-secondary text-white px-1 rounded small">${code}</code>`;
    };

    // Style links
    renderer.link = function(href, title, text) {
        const titleAttr = title ? ` title="${title}"` : '';
        return `<a href="${href}"${titleAttr} target="_blank" class="text-decoration-none">${text}</a>`;
    };

    // Style paragraphs
    renderer.paragraph = function(text) {
        return `<p class="mb-2">${text}</p>`;
    };

    // Style blockquotes
    renderer.blockquote = function(quote) {
        return `<blockquote class="blockquote border-start border-primary border-3 ps-3 ms-2 text-muted">${quote}</blockquote>`;
    };

    // Style tables
    renderer.table = function(header, body) {
        return `<div class="table-responsive"><table class="table table-sm table-bordered">${header}${body}</table></div>`;
    };

    marked.setOptions({ renderer: renderer });

    // Form validation
    $('form').on('submit', function(e) {
        const status = $('#shortlist_status').val();
        const remarks = $('#shortlist_remarks').val().trim();

        if (!status) {
            e.preventDefault();
            toastr.error('Please select a shortlisting status.');
            $('#shortlist_status').focus();
            return false;
        }

        if (status === 'eliminated' && !remarks) {
            e.preventDefault();
            toastr.error('Remarks are required when eliminating an application.');
            $('#shortlist_remarks').focus();
            return false;
        }

        return true;
    });

    // Auto-focus on status field
    $('#shortlist_status').focus();

    // Initialize markdown rendering for rating remarks
    <?php if (!empty($application['rating_remarks'])): ?>
        const ratingRemarks = <?= json_encode($application['rating_remarks']) ?>;
        renderMarkdownContent(ratingRemarks);
    <?php endif; ?>

    // Handle view mode toggle for rating remarks
    $(document).on('change', 'input[name="remarkViewMode"]', function() {
        if (this.id === 'remarkMarkdownView') {
            document.getElementById('rating_remarks_formatted').style.display = 'block';
            document.getElementById('rating_remarks_raw').style.display = 'none';
        } else {
            document.getElementById('rating_remarks_formatted').style.display = 'none';
            document.getElementById('rating_remarks_raw').style.display = 'block';
        }
    });
});

// Render markdown content using marked.js
function renderMarkdownContent(text) {
    if (!text) {
        document.getElementById('rating_remarks_formatted').innerHTML = '<p class="text-muted">No remarks available.</p>';
        return;
    }

    try {
        // Parse markdown with marked.js
        const html = marked.parse(text);

        // Basic XSS protection - remove script tags and dangerous attributes
        const cleanHtml = html
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/on\w+="[^"]*"/g, '')
            .replace(/javascript:/gi, '');

        document.getElementById('rating_remarks_formatted').innerHTML = cleanHtml;
    } catch (error) {
        console.error('Markdown parsing error:', error);
        document.getElementById('rating_remarks_formatted').innerHTML =
            '<p class="text-danger small">Error rendering markdown content.</p>';
    }
}
</script>
<?= $this->endSection() ?>
