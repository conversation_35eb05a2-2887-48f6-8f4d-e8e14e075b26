<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Application Shortlisting Result Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(to bottom left, #F00F00 0%, #D00D00 40%, #000000 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
        }
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom left, rgba(240, 15, 0, 0.1) 0%, rgba(0, 0, 0, 0.3) 100%);
        }
        .header h1 {
            position: relative;
            z-index: 1;
            margin: 0;
            font-size: 28px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .content {
            padding: 30px 25px;
            background: white;
        }
        .highlight-text {
            color: #F00F00;
            font-weight: bold;
        }
        .application-details {
            border-left: 4px solid #F00F00;
            padding: 20px;
            margin: 20px 0;
            background: linear-gradient(to right, rgba(240, 15, 0, 0.05) 0%, rgba(0, 0, 0, 0.02) 100%);
            border-radius: 5px;
        }
        .application-details p {
            margin: 5px 0;
            font-size: 14px;
        }
        .footer {
            background: linear-gradient(to bottom left, rgba(240, 15, 0, 0.03) 0%, rgba(0, 0, 0, 0.08) 100%);
            padding: 20px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        .status-message {
            border-left: 4px solid #F00F00;
            padding: 20px;
            margin: 20px 0;
            background: linear-gradient(to right, rgba(240, 15, 0, 0.05) 0%, rgba(0, 0, 0, 0.02) 100%);
            border-radius: 5px;
        }
        .status-message h3 {
            color: #F00F00;
            margin-top: 0;
            font-size: 20px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>
                <?php if ($notification_type === 'successful'): ?>
                    Congratulations! You've Been Shortlisted
                <?php else: ?>
                    Application Status Update
                <?php endif; ?>
            </h1>
        </div>

        <div class="content">
            <p>Dear <span class="highlight-text"><?= esc($applicant_name) ?></span>,</p>

            <?php if ($notification_type === 'successful'): ?>
                <div class="status-message">
                    <h3>Congratulations!</h3>
                    <p style="margin: 0; font-size: 16px;">Your application has been <strong>successfully shortlisted</strong> for the next stage of the recruitment process.</p>
                </div>

                <p>We are pleased to inform you that your application for the position below has been shortlisted:</p>
            <?php else: ?>
                <div class="status-message">
                    <h3>Application Status Update</h3>
                    <p style="margin: 0; font-size: 16px;">We regret to inform you that your application was <strong>not successful</strong> in the shortlisting stage.</p>
                </div>

                <p>Thank you for your interest in the position below. While your application was not successful this time, we encourage you to apply for future opportunities:</p>
            <?php endif; ?>

            <div class="application-details">
                <p><strong>Position Reference:</strong> <?= esc($position['position_reference']) ?></p>
                <p><strong>Position Name:</strong> <?= esc($position['designation']) ?></p>
                <p><strong>Advertisement Number:</strong> <?= esc($exercise['advertisement_no']) ?></p>
                <p><strong>Organization:</strong> <?= esc($organization['org_name']) ?></p>
                <p><strong>Gazette Number:</strong> <?= esc($exercise['gazzetted_no']) ?></p>
                <p><strong>Gazette Date:</strong> <?= date('F j, Y', strtotime($exercise['gazzetted_date'])) ?></p>
            </div>

            <?php if ($notification_type === 'successful'): ?>
                <p><strong>What happens next?</strong></p>
                <ul>
                    <li>You will be contacted regarding the next stage of the recruitment process</li>
                    <li>Please ensure your contact details remain up to date</li>
                    <li>Keep checking your email for further communications</li>
                    <li>Prepare for potential interviews or additional assessments</li>
                </ul>

                <p>We look forward to the next stage of the process with you.</p>
            <?php else: ?>
                <p><strong>Moving forward:</strong></p>
                <ul>
                    <li>We appreciate the time and effort you invested in your application</li>
                    <li>Your application will remain on file for future opportunities</li>
                    <li>We encourage you to apply for other suitable positions</li>
                    <li>Continue to check our website for new job openings</li>
                </ul>

                <p>Thank you for your interest in joining our organization.</p>
            <?php endif; ?>

            <p>If you have any questions regarding this notification, please contact the recruitment team through the official channels.</p>

            <p>Best Regards,<br><strong>The DERS</strong><br><?= esc($organization['org_name']) ?></p>
        </div>

        <div class="footer">
            <p>This is an automated message, please do not reply to this email.</p>
            <p>For inquiries, please contact the recruitment team through the official channels.</p>
            <p>&copy; <?= date('Y') ?> DERS - Dakoii Echad Recruitment & Selection System. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
