# CodeIgniter Google Cloud Storage Integration Guide

## Overview

This guide migrates the DERS (Digital Employment Recruitment System) from local file storage (`public/uploads/`) to Google Cloud Storage (GCS) while maintaining backward compatibility.

## Current System Analysis

### File Upload Categories
- **Applicant Files**: `public/uploads/applicants/{id}/`
- **Application Files**: `public/uploads/applications/`
- **Profile Photos**: `public/uploads/applicants/{id}/`
- **Organization Files**: `public/uploads/org_signatures/`
- **Job Descriptions**: `public/uploads/job_descriptions/`

### Current Pattern
```php
// File storage
'file_path' => 'public/uploads/category/' . $filename

// Database record
$fileData = [
    'file_path' => 'public/uploads/applicants/123/file.pdf',
    'created_by' => $userId
];
```

## Implementation Strategy

### Phase 1: Infrastructure Setup

#### Google Cloud Configuration
```bash
# Create service account
gcloud iam service-accounts create ders-storage \
    --display-name="DERS Storage Service"

# Grant permissions
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:ders-storage@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.admin"

# Create bucket
gsutil mb gs://ders-files-production
```

#### Environment Setup (.env)
```env
GCS_PROJECT_ID=your-project-id
GCS_BUCKET_NAME=ders-files-production
GCS_KEY_FILE_PATH=/path/to/service-key.json
GCS_ENABLE_FALLBACK=true
```

#### Composer Dependencies
```bash
composer require google/cloud-storage
```

### Phase 2: Service Implementation

#### GCS Service Class
**File:** `app/Services/GoogleCloudStorageService.php`

```php
<?php
namespace App\Services;

use Google\Cloud\Storage\StorageClient;
use CodeIgniter\Files\File;

class GoogleCloudStorageService
{
    private $storage;
    private $bucket;

    public function __construct()
    {
        $this->storage = new StorageClient([
            'projectId' => env('GCS_PROJECT_ID'),
            'keyFilePath' => env('GCS_KEY_FILE_PATH')
        ]);
        $this->bucket = $this->storage->bucket(env('GCS_BUCKET_NAME'));
    }

    public function uploadFile(File $file, string $category, array $metadata = []): array
    {
        try {
            $timestamp = time();
            $uniqueName = $timestamp . '_' . $file->getRandomName();
            $gcsPath = trim($category, '/') . '/' . $uniqueName;
            
            $object = $this->bucket->upload(
                file_get_contents($file->getRealPath()),
                ['name' => $gcsPath, 'metadata' => $metadata]
            );

            return [
                'success' => true,
                'gcs_path' => $gcsPath,
                'public_url' => $this->getPublicUrl($gcsPath),
                'signed_url' => $this->getSignedUrl($gcsPath),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType()
            ];
        } catch (\Exception $e) {
            log_message('error', 'GCS upload failed: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    public function getPublicUrl(string $gcsPath): string
    {
        return "https://storage.googleapis.com/" . env('GCS_BUCKET_NAME') . "/{$gcsPath}";
    }

    public function getSignedUrl(string $gcsPath, string $expiry = '+1 hour'): string
    {
        return $this->bucket->object($gcsPath)->signedUrl(new \DateTime($expiry));
    }

    public function deleteFile(string $gcsPath): bool
    {
        try {
            $this->bucket->object($gcsPath)->delete();
            return true;
        } catch (\Exception $e) {
            log_message('error', 'GCS delete failed: ' . $e->getMessage());
            return false;
        }
    }
}
```

#### Storage Helper
**File:** `app/Helpers/FileStorage_helper.php`

```php
<?php
use App\Services\GoogleCloudStorageService;

if (!function_exists('upload_to_storage')) {
    function upload_to_storage($file, string $category, array $metadata = []): array
    {
        // Try GCS first
        if (env('GCS_PROJECT_ID')) {
            try {
                $gcsService = new GoogleCloudStorageService();
                $result = $gcsService->uploadFile($file, $category, $metadata);
                
                if ($result['success']) {
                    return [
                        'success' => true,
                        'storage_type' => 'gcs',
                        'file_path' => 'gcs://' . $result['gcs_path'],
                        'public_url' => $result['public_url'],
                        'file_size' => $result['size']
                    ];
                }
            } catch (\Exception $e) {
                log_message('error', 'GCS failed, using local: ' . $e->getMessage());
            }
        }

        // Fallback to local storage
        return upload_to_local_storage($file, $category);
    }
}

if (!function_exists('upload_to_local_storage')) {
    function upload_to_local_storage($file, string $category): array
    {
        $newName = $file->getRandomName();
        $uploadPath = FCPATH . 'uploads/' . trim($category, '/') . '/';

        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        if ($file->move($uploadPath, $newName)) {
            return [
                'success' => true,
                'storage_type' => 'local',
                'file_path' => 'public/uploads/' . trim($category, '/') . '/' . $newName,
                'public_url' => base_url('uploads/' . trim($category, '/') . '/' . $newName),
                'file_size' => $file->getSize()
            ];
        }

        return ['success' => false, 'error' => 'Upload failed'];
    }
}

if (!function_exists('get_file_url')) {
    function get_file_url(string $filePath, bool $signed = false): ?string
    {
        if (strpos($filePath, 'gcs://') === 0) {
            $gcsPath = substr($filePath, 6);
            $gcsService = new GoogleCloudStorageService();
            return $signed ? $gcsService->getSignedUrl($gcsPath) : $gcsService->getPublicUrl($gcsPath);
        }
        return base_url($filePath);
    }
}
```

### Phase 3: Controller Updates

#### Updated Upload Method
```php
// Example: ApplicantController::uploadFile()
public function uploadFile()
{
    $applicant_id = session()->get('applicant_id');
    $file = $this->request->getFile('file');
    
    // Validate file
    $validation = \Config\Services::validation();
    $validation->setRules([
        'file' => 'uploaded[file]|max_size[file,25600]|ext_in[file,pdf,doc,docx,jpg,png]'
    ]);

    if (!$validation->withRequest($this->request)->run()) {
        return redirect()->back()->with('errors', $validation->getErrors());
    }

    // Upload using hybrid storage
    helper('FileStorage');
    $uploadResult = upload_to_storage($file, "applicants/{$applicant_id}", [
        'applicant_id' => $applicant_id,
        'title' => $this->request->getPost('file_title')
    ]);

    if (!$uploadResult['success']) {
        return redirect()->back()->with('error', 'Upload failed');
    }

    // Save to database
    $fileData = [
        'applicant_id' => $applicant_id,
        'file_title' => $this->request->getPost('file_title'),
        'file_path' => $uploadResult['file_path'],
        'storage_type' => $uploadResult['storage_type'],
        'file_size' => $uploadResult['file_size'],
        'created_by' => $applicant_id
    ];

    if ($this->filesModel->insert($fileData)) {
        return redirect()->back()->with('success', 'File uploaded successfully');
    } else {
        // Cleanup on database failure
        if ($uploadResult['storage_type'] === 'gcs') {
            $gcsService = new GoogleCloudStorageService();
            $gcsService->deleteFile(substr($uploadResult['file_path'], 6));
        }
        return redirect()->back()->with('error', 'Database save failed');
    }
}
```

### Phase 4: Database Migration

#### Add Storage Type Columns
```php
// Migration: AddStorageTypeToFiles.php
public function up()
{
    // Add to applicant_files
    $this->forge->addColumn('applicant_files', [
        'storage_type' => [
            'type' => 'ENUM',
            'constraint' => ['local', 'gcs'],
            'default' => 'local'
        ],
        'file_url' => [
            'type' => 'TEXT',
            'null' => true
        ]
    ]);

    // Add to appx_application_files
    $this->forge->addColumn('appx_application_files', [
        'storage_type' => [
            'type' => 'ENUM', 
            'constraint' => ['local', 'gcs'],
            'default' => 'local'
        ],
        'file_url' => [
            'type' => 'TEXT',
            'null' => true
        ]
    ]);
}
```

### Phase 5: View Updates

#### Enhanced File Display
```php
<!-- File listing with storage type indicator -->
<?php foreach ($files as $file): ?>
<div class="file-item <?= $file['storage_type'] === 'gcs' ? 'cloud-stored' : 'local-stored' ?>">
    <h5><?= esc($file['file_title']) ?></h5>
    <p>Storage: <?= strtoupper($file['storage_type']) ?></p>
    <a href="<?= get_file_url($file['file_path'], true) ?>" target="_blank" class="btn btn-primary">
        View File
    </a>
</div>
<?php endforeach; ?>

<style>
.cloud-stored { border-left: 4px solid #4285f4; }
.local-stored { border-left: 4px solid #34a853; }
</style>
```

#### Updated Upload Form
```html
<form enctype="multipart/form-data" method="POST">
    <div class="alert alert-info">
        <i class="fas fa-cloud"></i> Files uploaded to Google Cloud Storage for enhanced security and reliability.
    </div>
    
    <input type="file" name="file" required>
    <input type="text" name="file_title" placeholder="File Title" required>
    <textarea name="file_description" placeholder="Description"></textarea>
    
    <button type="submit">
        <i class="fas fa-cloud-upload-alt"></i> Upload to Cloud
    </button>
</form>
```

### Phase 6: Migration Tools

#### Local to GCS Migration Script
```php
// Command: php spark migrate:files-to-gcs
public function migrateFilesToGCS()
{
    $localFiles = $this->db->table('applicant_files')
                          ->where('storage_type', 'local')
                          ->get()
                          ->getResultArray();

    $migrated = 0;
    foreach ($localFiles as $file) {
        $localPath = ROOTPATH . $file['file_path'];
        
        if (!file_exists($localPath)) continue;

        // Upload to GCS
        $tempFile = new \CodeIgniter\Files\File($localPath);
        $gcsService = new GoogleCloudStorageService();
        
        $result = $gcsService->uploadFile($tempFile, dirname($file['file_path']), [
            'migrated_from' => 'local'
        ]);

        if ($result['success']) {
            // Update database
            $this->db->table('applicant_files')
                    ->where('id', $file['id'])
                    ->update([
                        'file_path' => 'gcs://' . $result['gcs_path'],
                        'storage_type' => 'gcs',
                        'file_url' => $result['public_url']
                    ]);
            $migrated++;
        }
    }

    echo "Migrated {$migrated} files to GCS\n";
}
```

## Deployment Checklist

### Pre-Deployment
- [ ] Google Cloud project configured
- [ ] Service account created with proper permissions  
- [ ] Bucket created and configured
- [ ] Environment variables set
- [ ] Dependencies installed
- [ ] Database migrations run

### Post-Deployment
- [ ] Test file uploads
- [ ] Verify file access URLs
- [ ] Check fallback to local storage
- [ ] Monitor error logs
- [ ] Run file migration if needed

### Security Considerations
- Store service account keys securely
- Use signed URLs for sensitive files
- Implement proper access controls
- Monitor storage costs
- Set up backup policies

### Benefits of This Implementation
- **Scalability**: Cloud storage scales automatically
- **Reliability**: 99.9% uptime guarantee
- **Security**: Enterprise-grade security
- **Cost-effective**: Pay only for what you use
- **Global access**: Files available worldwide
- **Backup**: Built-in redundancy
- **Fallback**: Graceful degradation to local storage

This hybrid approach ensures your application continues working even if GCS is temporarily unavailable, while providing the benefits of cloud storage when operational.# CodeIgniter Google Cloud Storage Integration Guide

## Overview

This guide migrates the DERS (Digital Employment Recruitment System) from local file storage (`public/uploads/`) to Google Cloud Storage (GCS) while maintaining backward compatibility.

## Current System Analysis

### File Upload Categories
- **Applicant Files**: `public/uploads/applicants/{id}/`
- **Application Files**: `public/uploads/applications/`
- **Profile Photos**: `public/uploads/applicants/{id}/`
- **Organization Files**: `public/uploads/org_signatures/`
- **Job Descriptions**: `public/uploads/job_descriptions/`

### Current Pattern
```php
// File storage
'file_path' => 'public/uploads/category/' . $filename

// Database record
$fileData = [
    'file_path' => 'public/uploads/applicants/123/file.pdf',
    'created_by' => $userId
];
```

## Implementation Strategy

### Phase 1: Infrastructure Setup

#### Google Cloud Configuration
```bash
# Create service account
gcloud iam service-accounts create ders-storage \
    --display-name="DERS Storage Service"

# Grant permissions
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:ders-storage@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.admin"

# Create bucket
gsutil mb gs://ders-files-production
```

#### Environment Setup (.env)
```env
GCS_PROJECT_ID=your-project-id
GCS_BUCKET_NAME=ders-files-production
GCS_KEY_FILE_PATH=/path/to/service-key.json
GCS_ENABLE_FALLBACK=true
```

#### Composer Dependencies
```bash
composer require google/cloud-storage
```

### Phase 2: Service Implementation

#### GCS Service Class
**File:** `app/Services/GoogleCloudStorageService.php`

```php
<?php
namespace App\Services;

use Google\Cloud\Storage\StorageClient;
use CodeIgniter\Files\File;

class GoogleCloudStorageService
{
    private $storage;
    private $bucket;

    public function __construct()
    {
        $this->storage = new StorageClient([
            'projectId' => env('GCS_PROJECT_ID'),
            'keyFilePath' => env('GCS_KEY_FILE_PATH')
        ]);
        $this->bucket = $this->storage->bucket(env('GCS_BUCKET_NAME'));
    }

    public function uploadFile(File $file, string $category, array $metadata = []): array
    {
        try {
            $timestamp = time();
            $uniqueName = $timestamp . '_' . $file->getRandomName();
            $gcsPath = trim($category, '/') . '/' . $uniqueName;
            
            $object = $this->bucket->upload(
                file_get_contents($file->getRealPath()),
                ['name' => $gcsPath, 'metadata' => $metadata]
            );

            return [
                'success' => true,
                'gcs_path' => $gcsPath,
                'public_url' => $this->getPublicUrl($gcsPath),
                'signed_url' => $this->getSignedUrl($gcsPath),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType()
            ];
        } catch (\Exception $e) {
            log_message('error', 'GCS upload failed: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    public function getPublicUrl(string $gcsPath): string
    {
        return "https://storage.googleapis.com/" . env('GCS_BUCKET_NAME') . "/{$gcsPath}";
    }

    public function getSignedUrl(string $gcsPath, string $expiry = '+1 hour'): string
    {
        return $this->bucket->object($gcsPath)->signedUrl(new \DateTime($expiry));
    }

    public function deleteFile(string $gcsPath): bool
    {
        try {
            $this->bucket->object($gcsPath)->delete();
            return true;
        } catch (\Exception $e) {
            log_message('error', 'GCS delete failed: ' . $e->getMessage());
            return false;
        }
    }
}
```

#### Storage Helper
**File:** `app/Helpers/FileStorage_helper.php`

```php
<?php
use App\Services\GoogleCloudStorageService;

if (!function_exists('upload_to_storage')) {
    function upload_to_storage($file, string $category, array $metadata = []): array
    {
        // Try GCS first
        if (env('GCS_PROJECT_ID')) {
            try {
                $gcsService = new GoogleCloudStorageService();
                $result = $gcsService->uploadFile($file, $category, $metadata);
                
                if ($result['success']) {
                    return [
                        'success' => true,
                        'storage_type' => 'gcs',
                        'file_path' => 'gcs://' . $result['gcs_path'],
                        'public_url' => $result['public_url'],
                        'file_size' => $result['size']
                    ];
                }
            } catch (\Exception $e) {
                log_message('error', 'GCS failed, using local: ' . $e->getMessage());
            }
        }

        // Fallback to local storage
        return upload_to_local_storage($file, $category);
    }
}

if (!function_exists('upload_to_local_storage')) {
    function upload_to_local_storage($file, string $category): array
    {
        $newName = $file->getRandomName();
        $uploadPath = FCPATH . 'uploads/' . trim($category, '/') . '/';

        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        if ($file->move($uploadPath, $newName)) {
            return [
                'success' => true,
                'storage_type' => 'local',
                'file_path' => 'public/uploads/' . trim($category, '/') . '/' . $newName,
                'public_url' => base_url('uploads/' . trim($category, '/') . '/' . $newName),
                'file_size' => $file->getSize()
            ];
        }

        return ['success' => false, 'error' => 'Upload failed'];
    }
}

if (!function_exists('get_file_url')) {
    function get_file_url(string $filePath, bool $signed = false): ?string
    {
        if (strpos($filePath, 'gcs://') === 0) {
            $gcsPath = substr($filePath, 6);
            $gcsService = new GoogleCloudStorageService();
            return $signed ? $gcsService->getSignedUrl($gcsPath) : $gcsService->getPublicUrl($gcsPath);
        }
        return base_url($filePath);
    }
}
```

### Phase 3: Controller Updates

#### Updated Upload Method
```php
// Example: ApplicantController::uploadFile()
public function uploadFile()
{
    $applicant_id = session()->get('applicant_id');
    $file = $this->request->getFile('file');
    
    // Validate file
    $validation = \Config\Services::validation();
    $validation->setRules([
        'file' => 'uploaded[file]|max_size[file,25600]|ext_in[file,pdf,doc,docx,jpg,png]'
    ]);

    if (!$validation->withRequest($this->request)->run()) {
        return redirect()->back()->with('errors', $validation->getErrors());
    }

    // Upload using hybrid storage
    helper('FileStorage');
    $uploadResult = upload_to_storage($file, "applicants/{$applicant_id}", [
        'applicant_id' => $applicant_id,
        'title' => $this->request->getPost('file_title')
    ]);

    if (!$uploadResult['success']) {
        return redirect()->back()->with('error', 'Upload failed');
    }

    // Save to database
    $fileData = [
        'applicant_id' => $applicant_id,
        'file_title' => $this->request->getPost('file_title'),
        'file_path' => $uploadResult['file_path'],
        'storage_type' => $uploadResult['storage_type'],
        'file_size' => $uploadResult['file_size'],
        'created_by' => $applicant_id
    ];

    if ($this->filesModel->insert($fileData)) {
        return redirect()->back()->with('success', 'File uploaded successfully');
    } else {
        // Cleanup on database failure
        if ($uploadResult['storage_type'] === 'gcs') {
            $gcsService = new GoogleCloudStorageService();
            $gcsService->deleteFile(substr($uploadResult['file_path'], 6));
        }
        return redirect()->back()->with('error', 'Database save failed');
    }
}
```

### Phase 4: Database Migration

#### Add Storage Type Columns
```php
// Migration: AddStorageTypeToFiles.php
public function up()
{
    // Add to applicant_files
    $this->forge->addColumn('applicant_files', [
        'storage_type' => [
            'type' => 'ENUM',
            'constraint' => ['local', 'gcs'],
            'default' => 'local'
        ],
        'file_url' => [
            'type' => 'TEXT',
            'null' => true
        ]
    ]);

    // Add to appx_application_files
    $this->forge->addColumn('appx_application_files', [
        'storage_type' => [
            'type' => 'ENUM', 
            'constraint' => ['local', 'gcs'],
            'default' => 'local'
        ],
        'file_url' => [
            'type' => 'TEXT',
            'null' => true
        ]
    ]);
}
```

### Phase 5: View Updates

#### Enhanced File Display
```php
<!-- File listing with storage type indicator -->
<?php foreach ($files as $file): ?>
<div class="file-item <?= $file['storage_type'] === 'gcs' ? 'cloud-stored' : 'local-stored' ?>">
    <h5><?= esc($file['file_title']) ?></h5>
    <p>Storage: <?= strtoupper($file['storage_type']) ?></p>
    <a href="<?= get_file_url($file['file_path'], true) ?>" target="_blank" class="btn btn-primary">
        View File
    </a>
</div>
<?php endforeach; ?>

<style>
.cloud-stored { border-left: 4px solid #4285f4; }
.local-stored { border-left: 4px solid #34a853; }
</style>
```

#### Updated Upload Form
```html
<form enctype="multipart/form-data" method="POST">
    <div class="alert alert-info">
        <i class="fas fa-cloud"></i> Files uploaded to Google Cloud Storage for enhanced security and reliability.
    </div>
    
    <input type="file" name="file" required>
    <input type="text" name="file_title" placeholder="File Title" required>
    <textarea name="file_description" placeholder="Description"></textarea>
    
    <button type="submit">
        <i class="fas fa-cloud-upload-alt"></i> Upload to Cloud
    </button>
</form>
```

### Phase 6: Migration Tools

#### Local to GCS Migration Script
```php
// Command: php spark migrate:files-to-gcs
public function migrateFilesToGCS()
{
    $localFiles = $this->db->table('applicant_files')
                          ->where('storage_type', 'local')
                          ->get()
                          ->getResultArray();

    $migrated = 0;
    foreach ($localFiles as $file) {
        $localPath = ROOTPATH . $file['file_path'];
        
        if (!file_exists($localPath)) continue;

        // Upload to GCS
        $tempFile = new \CodeIgniter\Files\File($localPath);
        $gcsService = new GoogleCloudStorageService();
        
        $result = $gcsService->uploadFile($tempFile, dirname($file['file_path']), [
            'migrated_from' => 'local'
        ]);

        if ($result['success']) {
            // Update database
            $this->db->table('applicant_files')
                    ->where('id', $file['id'])
                    ->update([
                        'file_path' => 'gcs://' . $result['gcs_path'],
                        'storage_type' => 'gcs',
                        'file_url' => $result['public_url']
                    ]);
            $migrated++;
        }
    }

    echo "Migrated {$migrated} files to GCS\n";
}
```

## Deployment Checklist

### Pre-Deployment
- [ ] Google Cloud project configured
- [ ] Service account created with proper permissions  
- [ ] Bucket created and configured
- [ ] Environment variables set
- [ ] Dependencies installed
- [ ] Database migrations run

### Post-Deployment
- [ ] Test file uploads
- [ ] Verify file access URLs
- [ ] Check fallback to local storage
- [ ] Monitor error logs
- [ ] Run file migration if needed

### Security Considerations
- Store service account keys securely
- Use signed URLs for sensitive files
- Implement proper access controls
- Monitor storage costs
- Set up backup policies

### Benefits of This Implementation
- **Scalability**: Cloud storage scales automatically
- **Reliability**: 99.9% uptime guarantee
- **Security**: Enterprise-grade security
- **Cost-effective**: Pay only for what you use
- **Global access**: Files available worldwide
- **Backup**: Built-in redundancy
- **Fallback**: Graceful degradation to local storage

This hybrid approach ensures your application continues working even if GCS is temporarily unavailable, while providing the benefits of cloud storage when operational.